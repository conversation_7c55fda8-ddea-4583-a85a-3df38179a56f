﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
	  <TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>
	<ItemGroup>
		<PackageReference Include="MediatR" Version="12.0.1" />
			<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.7" />
			<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.7">
			  <PrivateAssets>all</PrivateAssets>
			  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			</PackageReference>
			<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7" />
			<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.7">
			  <PrivateAssets>all</PrivateAssets>
			  <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
			</PackageReference>
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\..\Grassfish.Consulting.Bypass\Grassfish.Consulting.Ixm.Clients.csproj" />
		<ProjectReference Include="..\Domain\Domain.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Folder Include="Features\Players\Commands\" />
	</ItemGroup>
</Project>