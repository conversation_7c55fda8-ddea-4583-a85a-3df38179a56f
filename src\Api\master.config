<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<configuration>
	<appSettings type="global">
		<add key="MainServerURL" value="https://dev-sap-garden.grassfish.tv/gv2" />
	</appSettings>
	<appSettings type="Grassfish.SapGarden.ScenarioSwitcher">
		<add key="ScenarioSwitcherDbConnectionString" value="Data Source=(localdb)\MSSQLLocalDB;Initial Catalog=sap-garden;Integrated Security=True;Connect Timeout=30;Application Intent=ReadWrite;Multi Subnet Failover=False"/>
	</appSettings>
</configuration>