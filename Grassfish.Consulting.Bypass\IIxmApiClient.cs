﻿
using Grassfish.Consulting.Bypass.Clients.Api.V116;
using Grassfish.Consulting.Ixm.Core;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities;
using Grassfish.SapGarden.ScenarioSwitcher.Grassfish.Consulting.Ixm.Core;

namespace Grassfish.Consulting.Ixm.Clients;

public interface IIxmApiClient
{
    public Task<IxmUser?> GetUserAsync(CancellationToken cancellationToken = default);

    public Task<string?> GetJwtTokenAsync(CancellationToken cancellationToken = default);

    public Task<Guid> LoginAsync(IxmUsernamePasswordLoginRequest request, CancellationToken cancellationToken = default);

    public Task<ICollection<IxmPlayer>> GetPlayers(CancellationToken cancellationToken = default);

    public Task<ICollection<IxmScreenLayout>> GetScreenLayouts(CancellationToken cancellationToken = default);

    public Task<bool> DeactivateScreenLayout(int playerId, CancellationToken cancellationToken = default);

    public Task<bool> ActivateScreenLayouts(int playerId, List<ScreenLayoutAssignment> screenLayoutAssigments, CancellationToken cancellationToken = default);
}
