﻿using Grassfish.SapGarden.ScenarioSwitcher.Application.Abstractions.Data;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities;

using Microsoft.EntityFrameworkCore;

namespace Grassfish.SapGarden.ScenarioSwitcher.Infrastructure.Persistence.AppDb;

public class AppDbContext : DbContext, IAppDbContext
{
    public const string Schema = "ScenarioSwitcher";

    public DbSet<Scenario> Scenarios { get; set; }

    public DbSet<ScenarioScreenLayoutPlayer> ScenarioScreenLayoutPlayers { get; set; }

    public AppDbContext(DbContextOptions options) : base(options)
    {
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Register entity configurations
        modelBuilder.ApplyConfigurationsFromAssembly(this.GetType().Assembly);

        modelBuilder.HasDefaultSchema(Schema);

        // TODO: DB: Database entity configurations

        //modelBuilder.Entity<Player>()
        //    .HasMany(e => e.ScenarioScreenLayoutPlayers)
        //    .WithOne(e => e.Player)
        //    .HasForeignKey(e => e.PlayerId)
        //    .IsRequired();

        //modelBuilder.Entity<Scenario>()
        //    .HasMany(e => e.ScreenLayouts)
        //    .WithOne(e => e.Scenario)
        //    .HasForeignKey(e => e.ScenarioId)
        //    .IsRequired();

        //modelBuilder.Entity<ScenarioScreenLayout>()
        //    .HasMany(e => e.ScenarioScreenLayoutPlayers)
        //    .WithOne(e => e.ScreenLayout)
        //    .HasForeignKey(e => e.ScreenLayoutId)
        //    .IsRequired();
    }
}