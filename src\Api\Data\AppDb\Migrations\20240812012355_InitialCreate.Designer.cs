﻿// <auto-generated />
using System;
using Grassfish.SapGarden.ScenarioSwitcher.Infrastructure.Persistence.AppDb;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Data.AppDb.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20240812012355_InitialCreate")]
    partial class InitialCreate
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("ScenarioSwitcher")
                .HasAnnotation("ProductVersion", "8.0.7")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities.Scenario", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Scenarios", "ScenarioSwitcher");
                });

            modelBuilder.Entity("Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities.ScenarioScreenLayoutPlayer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("IxmPlayerId")
                        .HasColumnType("int");

                    b.Property<int>("IxmScreenLayoutId")
                        .HasColumnType("int");

                    b.Property<Guid>("ScenarioId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ScenarioId");

                    b.ToTable("ScenarioScreenLayoutPlayers", "ScenarioSwitcher");
                });

            modelBuilder.Entity("Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities.ScenarioScreenLayoutPlayer", b =>
                {
                    b.HasOne("Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities.Scenario", "Scenario")
                        .WithMany("ScenarioScreenLayoutPlayers")
                        .HasForeignKey("ScenarioId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Scenario");
                });

            modelBuilder.Entity("Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities.Scenario", b =>
                {
                    b.Navigation("ScenarioScreenLayoutPlayers");
                });
#pragma warning restore 612, 618
        }
    }
}
