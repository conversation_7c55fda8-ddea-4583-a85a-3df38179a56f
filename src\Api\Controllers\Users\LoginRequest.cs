﻿using System.ComponentModel.DataAnnotations;

namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Controllers.Users;

public class LoginRequest
{
    [Required]
    public required string Application { get; set; }

    [Required]
    public required string Username { get; set; }

    [Required]
    public required string Password { get; set; }

    public bool IsValid => !string.IsNullOrWhiteSpace(this.Application)
        && !string.IsNullOrWhiteSpace(this.Password)
        && !string.IsNullOrWhiteSpace(this.Username);
}
