using Grassfish.Consulting.Auth;
using Grassfish.Consulting.Ixm.Clients;
using Grassfish.Extensions.Configuration.MasterConfig;
using Grassfish.Extensions.Logging.Serilog.Server;
using Grassfish.SapGarden.ScenarioSwitcher.Api.Configuration;
using Grassfish.SapGarden.ScenarioSwitcher.Api.Services;
using Grassfish.SapGarden.ScenarioSwitcher.Application;
using Grassfish.SapGarden.ScenarioSwitcher.Application.Abstractions.Data;
using Grassfish.SapGarden.ScenarioSwitcher.Infrastructure.Persistence.AppDb;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authorization;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using Serilog;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;

namespace Api;

public class Program
{
    public static async Task Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        // GF MasterConfig:
        builder.Configuration.AddMasterConfig();
        var masterConfig = builder.Configuration.Get<MasterConfig>()!;
        masterConfig.Validate();
        builder.Services.AddTransient(x => masterConfig);

        // GF Server Serilog:
        if (!builder.Environment.IsDevelopment())
        {
            // Use console in development.
            builder.Logging.AddGfServerSerilog(builder.Configuration, "ScenarioSwitcher", namespacePrefixToRemove: "ScenarioSwitcher");
        }

        builder.Services.AddControllers(o => o.SuppressAsyncSuffixInActionNames = true);

        // Swagger:
        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        builder.Services.AddEndpointsApiExplorer();

        // Add HTTP Context Accessor:
        builder.Services.AddHttpContextAccessor();

        // IXM API Client Options:
        builder.Services
            .Configure<IxmApiClientOptions>(options => options.IxmStandardApiBaseUrl = masterConfig.MainServerUrl + "/webservices/API/");

        builder.Services.AddScoped<IxmApiClient>(sp =>
        {
            var http = sp.GetRequiredService<IHttpContextAccessor>();

            var ixmApiClient = ActivatorUtilities.CreateInstance<IxmApiClient>(sp);

            return ixmApiClient;
        });

        builder.Services.AddLogging(loggingBuilder =>
            loggingBuilder.AddSerilog(dispose: true));

        // Add IxmApiClient
        builder.Services.AddScoped<IIxmApiClient, IxmApiClient>();

        // TODO: ARCH: Split-off infrastructure
        builder.Services
            .AddApplication();

        builder.Services
            .AddDbContext<AppDbContext>(opt =>
                opt.UseSqlServer(
                    // Main connection string
                    connectionString: masterConfig.ScenarioSwitcherDbConnectionString,
                    // Set Migrations History Table
                    sqlServerOptionsAction: optBuilder => optBuilder.MigrationsHistoryTable(tableName: "__EFMigrationsHistory", schema: AppDbContext.Schema)
                )
            )
            .AddScoped<IAppDbContext>(sp => sp.GetRequiredService<AppDbContext>());

        builder.Services
            .AddScoped<IScenariosService, ScenariosService>();

        ConfigureSwagger(builder);

        builder.Services
            .AddAuthenticationCore(x =>
            {
                x.AddScheme<IxmAuthenticationHandler>(
                    IxmAuthenticationSchemeOptions.SCHEME_NAME,
                    IxmAuthenticationSchemeOptions.SCHEME_NAME
                );

                x.AddScheme<IxmAuthenticationHandler>(
                    JwtBearerDefaults.AuthenticationScheme,
                    JwtBearerDefaults.AuthenticationScheme
                );

                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            });

        builder.Services
            .AddAuthorization(options =>
            {
                var defaultAuthorizationPolicyBuilder = new AuthorizationPolicyBuilder
                (
                    IxmAuthenticationSchemeOptions.SCHEME_NAME,
                    JwtBearerDefaults.AuthenticationScheme
                );

                defaultAuthorizationPolicyBuilder = defaultAuthorizationPolicyBuilder.RequireAuthenticatedUser();

                options.DefaultPolicy = defaultAuthorizationPolicyBuilder.Build();
            });


        var app = builder.Build();

        // User Swagger:
        app.UseSwagger();
        app.UseSwaggerUI();

        // Run DB migrations:
        using (var scope = app.Services.CreateScope())
        {
            var db = scope.ServiceProvider.GetRequiredService<AppDbContext>();
            await db.Database.MigrateAsync();
        }

        app.UseHttpsRedirection();

        app.MapControllers();

        app.UseAuthentication();
        app.UseAuthorization();

        app.Run();
    }

    private static void ConfigureSwagger(WebApplicationBuilder builder)
    {
        // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
        builder.Services.AddEndpointsApiExplorer();

        builder.Services.AddSwaggerGen(config =>
        {
            var version = Assembly.GetExecutingAssembly().GetName().Version!.ToString();

            config.SwaggerDoc("v1", new OpenApiInfo { Title = "Scenario Switcher", Version = version });

            AddSwaggerApiKeySecurity(config);
            AddSwaggerSessionIdSecurity(config);
            AddSwaggerBearerSecurity(config);
        });
    }

    // TODO: CLUTTER: Move to Grassfish.Consulting.Auth.Extensions(SwaggerGen)
    private static void AddSwaggerBearerSecurity(SwaggerGenOptions config)
    {
        var bearerSecurityDefinition = new OpenApiSecurityScheme
        {
            Description = @"JWT Authorization header using the Bearer scheme. \r\n\r\n 
                      Enter 'Bearer' [space] and then your token in the text input below.
                      \r\n\r\nExample: 'Bearer 12345abcdef'",
            Name = "Authorization",
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Scheme = JwtBearerDefaults.AuthenticationScheme
        };

        config.AddSecurityDefinition(JwtBearerDefaults.AuthenticationScheme, bearerSecurityDefinition);

        var bearerSecurityScheme = new OpenApiSecurityScheme
        {
            Reference = new OpenApiReference
            {
                Type = ReferenceType.SecurityScheme,
                Id = JwtBearerDefaults.AuthenticationScheme
            },
            Scheme = "oauth2",
            Name = "Bearer token",
            In = ParameterLocation.Header,
        };

        var bearerSecurityRequirement = new OpenApiSecurityRequirement
        {
            { bearerSecurityScheme, new List<string>() },
        };

        config.AddSecurityRequirement(bearerSecurityRequirement);
    }

    // TODO: CLUTTER: Move to Grassfish.Consulting.Auth.Extensions(SwaggerGen)
    private static void AddSwaggerApiKeySecurity(SwaggerGenOptions config)
    {
        var apiKeySecurityDefinition = new OpenApiSecurityScheme
        {
            Name = IxmAuthenticationSchemeOptions.HEADER_XAPIKEY,
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Description = $"API Key provided in {IxmAuthenticationSchemeOptions.HEADER_XAPIKEY} header.",
            Scheme = IxmAuthenticationSchemeOptions.SCHEME_NAME
        };

        config.AddSecurityDefinition("ApiKey", apiKeySecurityDefinition);

        var key = new OpenApiSecurityScheme()
        {
            Reference = new OpenApiReference
            {
                Type = ReferenceType.SecurityScheme,
                Id = "ApiKey"
            },
            In = ParameterLocation.Header
        };
        var requirement = new OpenApiSecurityRequirement
        {
           { key, new List<string>() }
        };

        config.AddSecurityRequirement(requirement);
    }

    // TODO: CLUTTER: Move to Grassfish.Consulting.Auth.Extensions(SwaggerGen)
    private static void AddSwaggerSessionIdSecurity(SwaggerGenOptions config)
    {
        var apiKeySecurityDefinition = new OpenApiSecurityScheme
        {
            Name = IxmAuthenticationSchemeOptions.HEADER_XSESSIONID,
            In = ParameterLocation.Header,
            Type = SecuritySchemeType.ApiKey,
            Description = $"Session ID provided in {IxmAuthenticationSchemeOptions.HEADER_XSESSIONID} header.",
            Scheme = IxmAuthenticationSchemeOptions.SCHEME_NAME
        };

        config.AddSecurityDefinition("SessionId", apiKeySecurityDefinition);

        var key = new OpenApiSecurityScheme()
        {
            Reference = new OpenApiReference
            {
                Type = ReferenceType.SecurityScheme,
                Id = "SessionId"
            },
            In = ParameterLocation.Header
        };
        var requirement = new OpenApiSecurityRequirement
        {
           { key, new List<string>() }
        };

        config.AddSecurityRequirement(requirement);
    }
}