﻿namespace Grassfish.Consulting.Ixm.Core;

public class IxmUser
{
    /// <summary>
    /// Unique ID of the user
    /// </summary>

    [System.Text.Json.Serialization.JsonPropertyName("Id")]
    public int? Id { get; set; } = default!;

    /// <summary>
    /// Type of the user
    /// </summary>

    [System.Text.Json.Serialization.JsonPropertyName("Type")]
    [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
    public IxmUserType? Type { get; set; } = default!;

    /// <summary>
    /// Unique ID of the customer
    /// </summary>

    [System.Text.Json.Serialization.JsonPropertyName("CustomerId")]
    public int? CustomerId { get; set; } = default!;
}