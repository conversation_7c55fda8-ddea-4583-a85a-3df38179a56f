﻿using Grassfish.SapGarden.ScenarioSwitcher.Application.Features.Boxes.Queries;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Exceptions.Base;

using MediatR;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Controllers.Boxes;

[ApiController]
[Route("[controller]")]
public class PlayersController : ControllerBase
{
    private readonly ILogger<PlayersController> _logger;
    private readonly ISender _sender;

    public PlayersController(ILogger<PlayersController> logger,
        ISender sender)
    {
        this._logger = logger;
        this._sender = sender;
    }

    [HttpGet]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<List<IxmPlayer>>> GetAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetPlayersQuery();
            var players = await this._sender.Send(query, cancellationToken: cancellationToken);

            if (players == null
                || players.Count == 0)
            {
                return this.NoContent();
            }

            return this.Ok(players);
        }
        catch (Exception ex)
        {
            return this.BadRequest(ex.Message);
        }
    }
}
