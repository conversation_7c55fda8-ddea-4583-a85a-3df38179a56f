﻿using Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities;

namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Services;

public interface IScenariosService
{
    public Task<List<Domain.Entities.Scenario>> GetScenariosAsync();

    public Task<Domain.Entities.Scenario> CreateScenarioAsync(string name);

    public Task<Domain.Entities.Scenario> UpdateScenarioNameAsync(Guid scenarioId, string name);

    public Task<Scenario> DeleteAsync(Guid scenarioId);

    public Task ActivateAsync(Guid scenarioId);

    public Task<HashSet<int>> GetAssignedScreenLayoutIdsAsync(Guid scenarioId);

    public Task SetScenarioScreenLayoutPlayers(Guid scenarioId, int ixmScreenLayoutId, HashSet<int> playerIds);

    public Task RemoveScenarioScreenLayout(Guid scenarioId, int ixmScreenLayoutId);

    public Task<HashSet<int>> GetScenarioScreenLayoutPlayerIdsAsync(Guid scenarioId, int screenLayoutId);

}
