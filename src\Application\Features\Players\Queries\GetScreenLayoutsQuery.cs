﻿using Grassfish.Consulting.Ixm.Clients;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities;
using Grassfish.SapGarden.ScenarioSwitcher.Grassfish.Consulting.Ixm.Core;
using MediatR;

namespace Grassfish.SapGarden.ScenarioSwitcher.Application.Features.Boxes.Queries;

/// <summary>
/// Gets all the players in the system.
/// </summary>
public sealed record GetScreenLayoutsQuery() : IRequest<List<IxmScreenLayout>>;

public sealed class GetScreenLayoutsQueryHandler : IRequestHandler<GetScreenLayoutsQuery, List<IxmScreenLayout>>
{
    private readonly IIxmApiClient _ixmApi;

    public GetScreenLayoutsQueryHandler(IIxmApiClient ixmApi)
    {
        this._ixmApi = ixmApi;
    }

    public async Task<List<IxmScreenLayout>> Handle(GetScreenLayoutsQuery request, CancellationToken cancellationToken)
    {
        var ixmScreenLayouts = await this._ixmApi.GetScreenLayouts(cancellationToken: cancellationToken);

        return ixmScreenLayouts.ToList();
    }
}
