﻿using Grassfish.Consulting.Ixm.Clients;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Exceptions.Scenarios;
using Grassfish.SapGarden.ScenarioSwitcher.Infrastructure.Persistence.AppDb;
using Microsoft.EntityFrameworkCore;
using ApiEntity = Grassfish.Consulting.Bypass.Clients.Api.V116;

namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Services;

public class ScenariosService : IScenariosService
{
    private readonly AppDbContext _context;
    private readonly IIxmApiClient ixmApiClient;
    private readonly ILogger<ScenariosService> logger;

    public ScenariosService(AppDbContext context, IIxmApiClient ixmApiClient, ILogger<ScenariosService> logger)
    {
        this._context = context;
        this.ixmApiClient = ixmApiClient;
        this.logger = logger;
    }

    public async Task<List<Scenario>> GetScenariosAsync()
    {
        return await this._context.Scenarios
            .ToListAsync();
    }

    public async Task<Scenario> CreateScenarioAsync(string name)
    {
        var scenario = new Scenario { Id = new Guid(), Name = name };

        await _context.Scenarios.AddAsync(scenario);
        await _context.SaveChangesAsync();

        return scenario;
    }

    /// <summary>
    /// Updates <see cref="Scenario.Name"/>
    /// </summary>
    /// <param name="scenarioId">Id of the updated scenario.</param>
    /// <param name="name">Name to set.</param>
    /// <returns>Updated scenario.</returns>
    public async Task<Scenario> UpdateScenarioNameAsync(Guid scenarioId, string name)
    {
        var scenario = await this.GetScenarioByIdAsync(scenarioId);

        scenario.Name = name;

        _context.Scenarios.Update(scenario);

        await this._context.SaveChangesAsync();

        return scenario;
    }

    public async Task<Scenario> DeleteAsync(Guid scenarioId)
    {
        var scenario = await this.GetScenarioByIdAsync(scenarioId);

        _context.Scenarios.Remove(scenario);

        _context.SaveChanges();

        return scenario;
    }

    /// <exception cref="ScenarioNotFoundException">
    /// Thrown when scenario is not found for the provided ID (<paramref name="scenarioId"/>).
    /// </exception>
    public async Task ActivateAsync(Guid scenarioId)
    {
        var scenario = await this.GetScenarioByIdAsync(scenarioId);

        var scenarioScreenLayoutPlayers = await this._context.ScenarioScreenLayoutPlayers
            .Where(x => x.ScenarioId == scenario.Id)
            .Select(x => new { PlayerIxmId = x.IxmPlayerId, ScreenLayoutIxmId = x.IxmScreenLayoutId })
            .ToListAsync();

        foreach (var scenarioScreenLayoutPlayer in scenarioScreenLayoutPlayers)
        {
            var assignment = new ApiEntity.ScreenLayoutAssignment
            {
                ScreenNumber = 1,
                ScreenLayoutId = scenarioScreenLayoutPlayer.ScreenLayoutIxmId
            };

            // --- Workaround for IXM bug IXM-3628
            await ixmApiClient.DeactivateScreenLayout(scenarioScreenLayoutPlayer.PlayerIxmId);
            // ---

            await ixmApiClient.ActivateScreenLayouts(scenarioScreenLayoutPlayer.PlayerIxmId, [assignment]);
        }
    }

    /// <summary>
    /// Returns a list of screen layouts assigned to a scenario.
    /// </summary>
    /// <param name="scenarioId">Id of a scenario we're getting assigned screen layouts for.</param>
    /// <returns>List of screen layouts assigned to a scenario.</returns>
    /// <exception cref="ScenarioNotFoundException">
    /// Thrown when scenario is not found for the provided ID (<paramref name="scenarioId"/>).
    /// </exception>
    public async Task<HashSet<int>> GetAssignedScreenLayoutIdsAsync(Guid scenarioId)
    {
        var scenario = await this.GetScenarioByIdAsync(scenarioId);

        var screenLayouts = this._context.ScenarioScreenLayoutPlayers
            .Where(x => x.ScenarioId == scenarioId)
            .Select(x => x.IxmScreenLayoutId)
            .ToHashSet();

        return screenLayouts;
    }

    /// <exception cref="ScenarioNotFoundException">
    /// Thrown when scenario is not found for the provided ID (<paramref name="scenarioId"/>).
    /// </exception>
    public async Task SetScenarioScreenLayoutPlayers(Guid scenarioId, int ixmScreenLayoutId, HashSet<int> playerIds)
    {
        var scenario = await this.GetScenarioByIdAsync(scenarioId);

        using var trans = await this._context.Database.BeginTransactionAsync();

        await this._context.ScenarioScreenLayoutPlayers
            .Where(x => x.ScenarioId == scenarioId
                        && x.IxmScreenLayoutId == ixmScreenLayoutId)
            .ExecuteDeleteAsync();

        var newScenarioScreenLayouts = playerIds
            .Select(x => new ScenarioScreenLayoutPlayer { ScenarioId = scenarioId, IxmScreenLayoutId = ixmScreenLayoutId, IxmPlayerId = x })
            .ToList();

        await this._context.ScenarioScreenLayoutPlayers.AddRangeAsync(newScenarioScreenLayouts);

        await this._context.SaveChangesAsync();

        await trans.CommitAsync();
    }


    /// <summary>
    /// Returns a <see cref="Scenario"/> for a given <paramref name="scenarioId"/>.
    /// </summary>
    /// <param name="scenarioId">ID of a <see cref="Scenario"/>.</param>
    /// <returns>A scenario for given <paramref name="scenarioId"/>.</returns>
    /// <exception cref="ScenarioNotFoundException">Thrown if scenario not found for given <paramref name="scenarioId"/>.</exception>
    private async Task<Scenario> GetScenarioByIdAsync(Guid scenarioId)
    {
        var scenario = await _context.Scenarios.FirstOrDefaultAsync(s => s.Id == scenarioId);

        return scenario! ?? throw new ScenarioNotFoundException(scenarioId);
    }

    /// <exception cref="ScenarioNotFoundException">Thrown if scenario not found for given <paramref name="scenarioId"/>.</exception>
    public async Task RemoveScenarioScreenLayout(Guid scenarioId, int ixmScreenLayoutId)
    {
        var scenario = await this.GetScenarioByIdAsync(scenarioId);

        var screenLayoutsToDelete = await this._context.ScenarioScreenLayoutPlayers
            .Where(x => x.ScenarioId == scenarioId
                     && x.IxmScreenLayoutId == ixmScreenLayoutId)
            .ExecuteDeleteAsync();
    }

    public async Task<HashSet<int>> GetScenarioScreenLayoutPlayerIdsAsync(Guid scenarioId, int ixmScreenLayoutId)
    {
        var screenLayoutIdPlayers = await _context.ScenarioScreenLayoutPlayers
            .Where(x => x.ScenarioId == scenarioId
                        && x.IxmScreenLayoutId == ixmScreenLayoutId)
            .Select(x => x.IxmPlayerId)
            .ToListAsync();

        return screenLayoutIdPlayers.ToHashSet();
    }
}
