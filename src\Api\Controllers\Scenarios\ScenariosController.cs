﻿using Grassfish.SapGarden.ScenarioSwitcher.Api.Services;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Exceptions.Base;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Exceptions.Scenarios;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Controllers.Scenarios;

[ApiController]
[Route("[controller]")]
public class ScenariosController : Controller
{
    private readonly ILogger<ScenariosController> _logger;
    private readonly IScenariosService scenariosService;

    public ScenariosController(ILogger<ScenariosController> logger, IScenariosService scenariosService)
    {
        this._logger = logger;
        this.scenariosService = scenariosService;
    }

    [HttpGet]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<List<Scenario>>> GetAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var scenarios = await this.scenariosService.GetScenariosAsync();

            if (scenarios.Count == 0)
            {
                return this.NoContent();
            }

            return this.Ok(scenarios);
        }
        catch (Exception ex)
        {
            return this.BadRequest(ex.Message);
        }
    }

    [HttpPost]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<Scenario>> PostAsync([FromBody] string name, CancellationToken cancellationToken = default)
    {
        var newScenario = await this.scenariosService.CreateScenarioAsync(name);

        return this.Ok(newScenario);
    }

    [HttpPut("{scenarioId:guid}")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<Scenario>> UpdateAsync(Guid scenarioId, [FromBody] string name, CancellationToken cancellationToken = default)
    {
        try
        {
            var scenario = await this.scenariosService.UpdateScenarioNameAsync(scenarioId, name);

            return this.Ok(scenarioId);
        }
        catch (EntityNotFoundException ex)
        {
            return this.NotFound(ex.Message);
        }
    }

    [HttpDelete("{scenarioId:guid}")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status403Forbidden)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Scenario>> DeleteAsync(Guid scenarioId, CancellationToken cancellationToken = default)
    {
        try
        {
            var removedObj = await this.scenariosService.DeleteAsync(scenarioId);

            return this.Ok(removedObj);
        }
        catch (ScenarioNotFoundException ex)
        {
            return this.NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return this.BadRequest(ex.Message);
        }
    }

    [HttpPost("{scenarioId:guid}/activate")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<bool>> ActivateAsync(Guid scenarioId, CancellationToken cancellationToken = default)
    {
        try
        {
            await this.scenariosService.ActivateAsync(scenarioId);

            return this.Ok();
        }
        catch (DomainValidationException ex)
        {
            return this.BadRequest(ex.Message);
        }
    }

    [HttpGet("{scenarioId:guid}/screen-layouts")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<HashSet<int>>> GetAssignedScreenLayouts(Guid scenarioId, CancellationToken cancellationToken = default)
    {
        try
        {
            var screenLayouts = await this.scenariosService.GetAssignedScreenLayoutIdsAsync(scenarioId);

            if (screenLayouts.Count == 0)
            {
                return this.NoContent();
            }

            return this.Ok(screenLayouts);
        }
        catch (ScenarioNotFoundException ex)
        {
            return this.NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return this.BadRequest(ex.Message);
        }

    }

    [HttpPut("{scenarioId:guid}/screen-layouts/{ixmScreenLayoutId:int}/players")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult> SetScenarioScreenLayoutPlayers(Guid scenarioId, int ixmScreenLayoutId, [FromBody] HashSet<int> ixmPlayerIds, CancellationToken cancellationToken = default)
    {
        try
        {
            await this.scenariosService.SetScenarioScreenLayoutPlayers(scenarioId, ixmScreenLayoutId, ixmPlayerIds);

            return this.Ok();
        }
        catch (ScenarioNotFoundException ex)
        {
            return this.NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return this.BadRequest(ex.Message);
        }
    }

    [HttpDelete("{scenarioId:guid}/screen-layouts/{ixmScreenLayoutId:int}")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult> SetScenarioScreenLayoutPlayers(Guid scenarioId, int ixmScreenLayoutId, CancellationToken cancellationToken = default)
    {
        try
        {
            await this.scenariosService.RemoveScenarioScreenLayout(scenarioId, ixmScreenLayoutId);

            return this.Ok();
        }
        catch (ScenarioNotFoundException ex)
        {
            return this.NotFound(ex.Message);
        }
        catch (Exception ex)
        {
            return this.BadRequest(ex.Message);
        }
    }

    [HttpGet("{scenarioId:guid}/screen-layouts/{ixmScreenLayoutId:int}/players")]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<HashSet<int>>> GetScenarioScreenlayoutPlayerIds(Guid scenarioId, int ixmScreenLayoutId, CancellationToken cancellationToken = default)
    {
        try
        {
            var screenLayoutPlayerIds = await this.scenariosService.GetScenarioScreenLayoutPlayerIdsAsync(scenarioId, ixmScreenLayoutId);

            if (screenLayoutPlayerIds.Count == 0)
            {
                return NoContent();
            }

            return this.Ok(screenLayoutPlayerIds);
        }
        catch (Exception ex)
        {
            return this.BadRequest(ex.Message);
        }
    }
}
