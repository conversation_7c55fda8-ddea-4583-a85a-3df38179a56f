﻿using System.Text.Json;

namespace Grassfish.SapGarden.ScenarioSwitcher.Domain.Exceptions.Base;

public class EntityNotFoundException : DomainException
{
    public EntityNotFoundException(object query) : base(SerializeQueryObject(query))
    {
    }

    private static string SerializeQueryObject(object query)
    {
        if (query is string queryString)
        {
            return queryString;
        }

        return JsonSerializer.Serialize(query);
    }
}
