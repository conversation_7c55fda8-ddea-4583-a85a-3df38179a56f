# Grassfish Consulting - Standalone Web Service Template

## TODO
* TEMPLATE.md - Solution structure documentation
* TEMPLATE.md - Clean architecture
* TEMPLATE.md - Document configuration options
* Add README.md template
* Upgrade to LTS .NET - currently at .NET 7

## Installing the template

Clone the repository:

```<NAME_EMAIL>:grassfishbucket/gf-consulting-dach-dotnet-templates-standalonewebservice.git```

Navigate to the root of the repository:

``cd gf-consulting-dach-dotnet-templates-standalonewebservice``

Install the template:

``dotnet new install ./``