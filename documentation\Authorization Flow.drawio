<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********" version="24.7.6">
  <diagram name="Page-1" id="kc73UN-MPFH6u6Mmc7R0">
    <mxGraphModel dx="1147" dy="611" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="QQb9hG137RK5Z0eGdfOG-20" value="" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;strokeColor=none;fillColor=#994C00;opacity=10;" vertex="1" parent="1">
          <mxGeometry x="94" y="583" width="390" height="130" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-18" value="" style="rounded=0;whiteSpace=wrap;html=1;dashed=1;strokeColor=none;fillColor=#006666;opacity=10;" vertex="1" parent="1">
          <mxGeometry x="94" y="294" width="390" height="246" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-1" value="" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;dropTarget=0;collapsible=0;recursiveResize=0;outlineConnect=0;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};participant=umlActor;" vertex="1" parent="1">
          <mxGeometry x="120" y="240" width="20" height="480" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-2" value="Scenario Switcher" style="shape=umlLifeline;perimeter=lifelinePerimeter;whiteSpace=wrap;html=1;container=1;dropTarget=0;collapsible=0;recursiveResize=0;outlineConnect=0;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="1">
          <mxGeometry x="370" y="240" width="140" height="480" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-5" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="QQb9hG137RK5Z0eGdfOG-2">
          <mxGeometry x="65" y="78" width="10" height="80" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-12" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="QQb9hG137RK5Z0eGdfOG-2">
          <mxGeometry x="65" y="198" width="10" height="80" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-4" value="API User" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="100" y="210" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-8" value="&lt;span style=&quot;font-weight: normal;&quot;&gt;Body: { application, username,&amp;nbsp;&lt;span style=&quot;background-color: initial;&quot;&gt;password}&lt;/span&gt;&lt;/span&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="130" y="323" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-6" value="POST /Users/<USER>" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;entryX=0;entryY=0;entryDx=0;entryDy=5;" edge="1" target="QQb9hG137RK5Z0eGdfOG-5" parent="1" source="QQb9hG137RK5Z0eGdfOG-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="323" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-7" value="${sessionId}" style="html=1;verticalAlign=bottom;endArrow=open;dashed=1;endSize=8;curved=0;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=-5;" edge="1" source="QQb9hG137RK5Z0eGdfOG-5" parent="1" target="QQb9hG137RK5Z0eGdfOG-1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="210" y="393" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-10" value="POST /Users/<USER>" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;entryX=0;entryY=0;entryDx=0;entryDy=5;" edge="1" parent="1">
          <mxGeometry x="0.0005" relative="1" as="geometry">
            <mxPoint x="132" y="443" as="sourcePoint" />
            <mxPoint x="437" y="443" as="targetPoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-11" value="${jwtToken}" style="html=1;verticalAlign=bottom;endArrow=open;dashed=1;endSize=8;curved=0;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=-5;" edge="1" parent="1">
          <mxGeometry x="-0.0361" y="-3" relative="1" as="geometry">
            <mxPoint x="132" y="513" as="targetPoint" />
            <mxPoint x="437" y="513" as="sourcePoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-13" value="Header[&quot;X-SessionId&quot;]: ${sessionId}" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="130" y="440" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-15" value="GET /Scenarios" style="html=1;verticalAlign=bottom;endArrow=block;curved=0;rounded=0;entryX=0;entryY=0;entryDx=0;entryDy=5;" edge="1" parent="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="132" y="615" as="sourcePoint" />
            <mxPoint x="437" y="615" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-16" value="[ { id, name } ]" style="html=1;verticalAlign=bottom;endArrow=open;dashed=1;endSize=8;curved=0;rounded=0;exitX=0;exitY=1;exitDx=0;exitDy=-5;" edge="1" parent="1">
          <mxGeometry x="-0.0361" y="-3" relative="1" as="geometry">
            <mxPoint x="132" y="685" as="targetPoint" />
            <mxPoint x="437" y="685" as="sourcePoint" />
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-17" value="Header[&quot;Authorization&quot;]: Bearer ${jwtToken}" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="130" y="612" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-14" value="" style="html=1;points=[[0,0,0,0,5],[0,1,0,0,-5],[1,0,0,0,5],[1,1,0,0,-5]];perimeter=orthogonalPerimeter;outlineConnect=0;targetShapes=umlLifeline;portConstraint=eastwest;newEdgeStyle={&quot;curved&quot;:0,&quot;rounded&quot;:0};" vertex="1" parent="1">
          <mxGeometry x="435" y="610" width="10" height="80" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-19" value="Auth Flow" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#006666;" vertex="1" parent="1">
          <mxGeometry x="259" y="264" width="60" height="30" as="geometry" />
        </mxCell>
        <mxCell id="QQb9hG137RK5Z0eGdfOG-21" value="API Requests" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#994C00;" vertex="1" parent="1">
          <mxGeometry x="243.5" y="553" width="91" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
