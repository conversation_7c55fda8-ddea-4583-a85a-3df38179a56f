﻿using Grassfish.Consulting.Auth;
using Grassfish.Consulting.Bypass.Clients.Api.V116;
using Grassfish.Consulting.Ixm.Core;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities;
using Grassfish.SapGarden.ScenarioSwitcher.Grassfish.Consulting.Ixm.Core;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace Grassfish.Consulting.Ixm.Clients;

public class IxmApiClient : HttpClient, IIxmApiClient
{
    private readonly IxmApiV116Client _v16;
    private readonly ILogger<IxmApiClient> _logger;
    private readonly IxmApiClientOptions _options;

    public IxmApiClient(IOptions<IxmApiClientOptions> options, IHttpContextAccessor httpContextAccessor, ILogger<IxmApiClient> logger)
    {
        this._options = options.Value;
        this.BaseAddress = new Uri(this._options.IxmStandardApiBaseUrl);
        this._logger = logger;

        this._v16 = new IxmApiV116Client(this);

        if (httpContextAccessor.HttpContext == null)
        {
            // TODO: Build Tests
            this._logger.LogError("No HTTP Context found! This shouldn't happen under normal operation. Possible issues in service injection. Contact developers.");

            return;
        }

        if (httpContextAccessor.HttpContext.Request.Headers.TryGetValue(IxmAuthenticationSchemeOptions.HEADER_XAPIKEY, out var apiKey))
        {
            this.DefaultRequestHeaders.Add(IxmAuthenticationSchemeOptions.HEADER_XAPIKEY, apiKey.AsEnumerable());
        }

        if (httpContextAccessor.HttpContext.Request.Headers.TryGetValue(IxmAuthenticationSchemeOptions.HEADER_XSESSIONID, out var session))
        {
            this.DefaultRequestHeaders.Add(IxmAuthenticationSchemeOptions.HEADER_XSESSIONID, session.AsEnumerable());
        }

        if (httpContextAccessor.HttpContext.Request.Headers.TryGetValue(IxmAuthenticationSchemeOptions.HEADER_AUTHORIZATION, out var authorization))
        {
            this.DefaultRequestHeaders.Add(IxmAuthenticationSchemeOptions.HEADER_AUTHORIZATION, authorization.AsEnumerable());
        }
    }

    public async Task<string?> GetJwtTokenAsync(CancellationToken cancellationToken = default)
    {
        var result = await this._v16.Users_GetTokenAsync(cancellationToken);

        if (string.IsNullOrWhiteSpace(result))
        {
            return null;
        }

        return result;
    }

    public async Task<IxmUser?> GetUserAsync(CancellationToken cancellationToken)
    {
        var ixmUser = await this._v16.Users_GetSessionUserAsync(cancellationToken: cancellationToken);

        if (ixmUser == null)
        {
            return null;
        }

        return new IxmUser { CustomerId = ixmUser.CustomerId, Id = ixmUser.Id, Type = (IxmUserType?)ixmUser.Type };
    }

    public Task<Guid> LoginAsync(IxmUsernamePasswordLoginRequest request, CancellationToken cancellationToken = default)
    {
        return this._v16.Users_LoginAsync(new Credentials { Application = request.Application, Password = request.Password, Username = request.Username }, cancellationToken: cancellationToken);
    }

    public async Task<ICollection<IxmPlayer>> GetPlayers(CancellationToken cancellationToken)
    {
        var players = await this._v16.Players_GetListAsync(cancellationToken: cancellationToken);

        if (players == null || players.Count == 0)
        {
            return [];
        }

        return players
            .Select(x => new IxmPlayer { IxmId = x.Id!.Value, IxmName = x.Name ?? "No name." })
            .ToList();
    }

    public async Task<ICollection<IxmScreenLayout>> GetScreenLayouts(CancellationToken cancellationToken)
    {
        var screenLayouts = await this._v16.ScreenLayouts_GetListAsync(cancellationToken: cancellationToken);

        if (screenLayouts == null || screenLayouts.Count == 0)
        {
            return [];
        }

        return screenLayouts
            .Select(x => new IxmScreenLayout { IxmId = x.Id!.Value, IxmName = x.Name ?? "No name." })
            .ToList();
    }

    public async Task<bool> ActivateScreenLayouts(int playerId, List<ScreenLayoutAssignment> screenLayoutAssigments, CancellationToken cancellationToken = default)
    {
        var retval = await this._v16.Players_SetAssignedScreenLayoutsAsync(playerId, screenLayoutAssigments, cancellationToken);

        return true;
    }

    public async Task<bool> DeactivateScreenLayout(int playerId, CancellationToken cancellationToken = default)
    {
        var retval = await this._v16.Players_SetAssignedScreenLayoutsAsync(playerId, [], cancellationToken);

        return true;
    }
}
