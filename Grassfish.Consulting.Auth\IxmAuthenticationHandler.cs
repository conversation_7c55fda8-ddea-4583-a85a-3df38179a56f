﻿using Grassfish.Consulting.Ixm.Clients;
using Grassfish.Consulting.Ixm.Core;
using Microsoft.AspNetCore.Authentication;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System.Security.Claims;
using System.Text.Encodings.Web;

namespace Grassfish.Consulting.Auth;

public class IxmAuthenticationHandler : AuthenticationHandler<AuthenticationSchemeOptions>
{
    private readonly IIxmApiClient _ixmClient;

    public IxmAuthenticationHandler(
        IOptionsMonitor<AuthenticationSchemeOptions> options,
        ILoggerFactory logger,
        IIxmApiClient ixmClient)
        : base(options, logger, UrlEncoder.Default, new SystemClock())
    {
        this._ixmClient = ixmClient;
    }

    protected override async Task<AuthenticateResult> HandleAuthenticateAsync()
    {
        IxmUser? ixmUser = null;

        try
        {
            ixmUser = await this._ixmClient.GetUserAsync();

            if (ixmUser == null
                || !ixmUser.Id.HasValue
                || !ixmUser.Type.HasValue
                || !ixmUser.CustomerId.HasValue)
            {
                return AuthenticateResult.Fail(new UnauthorizedAccessException());
            }

        }
        catch (Exception ex)
        {
            return AuthenticateResult.Fail(ex);
        }

        var claims = new List<Claim>()
        {
            new(IxmAuthenticationSchemeOptions.CLAIM_ID, ixmUser.Id!.ToString()!),
            new(IxmAuthenticationSchemeOptions.CLAIM_CUSTOMER_ID, ixmUser.CustomerId!.ToString()!),
            new(IxmAuthenticationSchemeOptions.CLAIM_USER_TYPE, ixmUser.Type!.ToString()!),
        };

        var claimsIdentity = new ClaimsIdentity(claims, this.Scheme.Name);

        var claimsPrincipal = new ClaimsPrincipal(claimsIdentity);

        var authTicket = new AuthenticationTicket(claimsPrincipal, this.Scheme.Name);

        return AuthenticateResult.Success(authTicket);
    }
}
