﻿using Grassfish.SapGarden.ScenarioSwitcher.Application.Features.Boxes.Queries;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Exceptions.Base;
using Grassfish.SapGarden.ScenarioSwitcher.Grassfish.Consulting.Ixm.Core;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Controllers.Boxes;

[ApiController]
[Route("screen-layouts")]
public class ScreenLayoutsController : ControllerBase
{
    private readonly ILogger<ScreenLayoutsController> _logger;
    private readonly IRequestHandler<GetScreenLayoutsQuery, List<IxmScreenLayout>> requestHandler;

    public ScreenLayoutsController(ILogger<ScreenLayoutsController> logger, IRequestHandler<GetScreenLayoutsQuery, List<IxmScreenLayout>> requestHandler)
    {
        this._logger = logger;
        this.requestHandler = requestHandler;
    }

    [HttpGet]
    [Authorize]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<List<IxmScreenLayout>>> GetAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var query = new GetScreenLayoutsQuery();
            var screenLayouts = await this.requestHandler.Handle(query, cancellationToken);

            return this.Ok(screenLayouts);
        }
        catch (DomainValidationException ex)
        {
            return this.BadRequest(ex.Message);
        }
    }
}
