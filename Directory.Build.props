<Project>
	<PropertyGroup>
		<!-- Custom Properties -->
		<CompanyName>Grassfish</CompanyName>
		<ProjectName>Grassfish.SapGarden.ScenarioSwitcher</ProjectName>
		<!-- Redefine MSBuild Properties -->
		<Company>$(CompanyName)</Company>
		<!-- General .NET project settings -->
		<Authors>$(CompanyName)</Authors>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<TargetFramework>net7.0</TargetFramework>
		<!-- Set namespace and assembly name conventions -->
		<RootNamespace>$(ProjectName).$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
		<AssemblyName>$(ProjectName).$(MSBuildProjectName)</AssemblyName>
	</PropertyGroup>
</Project>