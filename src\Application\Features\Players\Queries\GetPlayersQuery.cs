﻿using Grassfish.Consulting.Ixm.Clients;
using Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities;

using MediatR;

namespace Grassfish.SapGarden.ScenarioSwitcher.Application.Features.Boxes.Queries;

/// <summary>
/// Gets all the players in the system.
/// </summary>
public sealed record GetPlayersQuery() : IRequest<List<IxmPlayer>>;

public sealed class GetPlayersQueryHandler : IRequestHandler<GetPlayersQuery, List<IxmPlayer>>
{
    private readonly IIxmApiClient _ixmApi;

    public GetPlayersQueryHandler(IIxmApiClient ixmApi)
    {
        this._ixmApi = ixmApi;
    }

    public async Task<List<IxmPlayer>> Handle(GetPlayersQuery request, CancellationToken cancellationToken)
    {
        var ixmPlayers = await this._ixmApi.GetPlayers(cancellationToken: cancellationToken);

        return ixmPlayers.ToList();
    }
}
