﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Grassfish.Server.BusinessEntitiesCore" Version="11.16.2" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication" Version="2.2.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.2.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Grassfish.Consulting.Bypass\Grassfish.Consulting.Ixm.Clients.csproj" />
    <ProjectReference Include="..\Grassfish.Consulting.Ixm.Core\Grassfish.Consulting.Ixm.Core.csproj" />
  </ItemGroup>

</Project>
