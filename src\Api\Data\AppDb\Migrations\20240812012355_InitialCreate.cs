﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Data.AppDb.Migrations
{
    /// <inheritdoc />
    public partial class InitialCreate : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "ScenarioSwitcher");

            migrationBuilder.CreateTable(
                name: "Scena<PERSON>s",
                schema: "ScenarioSwitcher",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Name = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Scenarios", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ScenarioScreenLayoutPlayers",
                schema: "ScenarioSwitcher",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ScenarioId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    IxmScreenLayoutId = table.Column<int>(type: "int", nullable: false),
                    IxmPlayerId = table.Column<int>(type: "int", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ScenarioScreenLayoutPlayers", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ScenarioScreenLayoutPlayers_Scenarios_ScenarioId",
                        column: x => x.ScenarioId,
                        principalSchema: "ScenarioSwitcher",
                        principalTable: "Scenarios",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ScenarioScreenLayoutPlayers_ScenarioId",
                schema: "ScenarioSwitcher",
                table: "ScenarioScreenLayoutPlayers",
                column: "ScenarioId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "ScenarioScreenLayoutPlayers",
                schema: "ScenarioSwitcher");

            migrationBuilder.DropTable(
                name: "Scenarios",
                schema: "ScenarioSwitcher");
        }
    }
}
