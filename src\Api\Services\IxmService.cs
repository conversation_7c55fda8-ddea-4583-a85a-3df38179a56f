﻿//namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Services
//{
//    public class IxmService
//    {
//        private readonly ILogger<IxmService> _logger;

//        public IxmService(ILogger<IxmService> logger)
//        {
//            this._logger = logger;
//        }

//        public async Task<string> GetTokenAsync(
//            string username,
//            string password,
//            string application,
//            CancellationToken cancellationToken = default)
//        {

//            if (string.IsNullOrWhiteSpace(application))
//            {
//                throw new ArgumentException("Argument can't be null or whitespace!", nameof(application));
//            }

//            if (string.IsNullOrWhiteSpace(username))
//            {
//                throw new ArgumentException("Argument can't be null or whitespace!", nameof(username));
//            }

//            if (string.IsNullOrWhiteSpace(password))
//            {
//                throw new ArgumentException("Argument can't be null or whitespace!", nameof(password));
//            }

//            var sessionId = await this.ixmApiClient.VUsersLoginAsync(
//                new Api.Credentials
//                {
//                    Application = credentials.Application,
//                    Username = credentials.Username,
//                    Password = credentials.Password,
//                },
//                this.config.Version,
//                ct);

//            this.ixmApiClient.SetSessionId(sessionId.ToString());
//            return await this.ixmApiClient.VUsersTokenGetAsync(this.config.Version, ct);
//        }

//    }
//}
