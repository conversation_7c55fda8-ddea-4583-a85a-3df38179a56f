﻿using Grassfish.Extensions.Configuration.MasterConfig;

namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Configuration;

public class MasterConfig : MasterConfigBase
{
    public string? ScenarioSwitcherDbConnectionString { get; set; }

    public void Validate()
    {
        if (string.IsNullOrEmpty(this.ScenarioSwitcherDbConnectionString))
        {
            throw new ArgumentNullException(nameof(ScenarioSwitcherDbConnectionString), $"Missing master.config property: {nameof(ScenarioSwitcherDbConnectionString)}.");
        }
    }
}
