﻿using System.ComponentModel.DataAnnotations;

namespace Grassfish.Consulting.Ixm.Core;

public class IxmUsernamePasswordLoginRequest
{
    [Required]
    public required string Application { get; set; }

    [Required]
    public required string Username { get; set; }

    [Required]
    public required string Password { get; set; }

    public bool IsValid => !string.IsNullOrWhiteSpace(this.Application)
        && !string.IsNullOrWhiteSpace(this.Password)
        && !string.IsNullOrWhiteSpace(this.Username);
}
