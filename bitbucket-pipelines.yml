definitions:
  runs-on: &self-hosted
    - 'self.hosted'
    - 'linux'
  steps:
  - step: &build-web-service
      name: Build WebService
      runs-on: *self-hosted
      caches:
        - dotnetcore
      script:         
        - export CONFIGURATION=Release
        - export PROJECT_NAME=Api
        - echo $NUGET_CONFIG_XML > ./nuget.config 
        - dotnet restore ./src/${PROJECT_NAME}/${PROJECT_NAME}.csproj
        - dotnet clean ./src/${PROJECT_NAME} --configuration $CONFIGURATION
        - dotnet build --no-restore ./src/${PROJECT_NAME} --configuration $CONFIGURATION --version-suffix build.${BITBUCKET_BUILD_NUMBER} 
        - dotnet publish --configuration $CONFIGURATION --no-build ./src/${PROJECT_NAME} -o ./publish_web_service
        - ls ./publish_web_service
      artifacts: 
        - publish_web_service/**
  - step: &build-web-service-tag
      name: Build WebService
      runs-on: *self-hosted
      caches:
        - dotnetcore
      script:         
        - export CONFIGURATION=Release
        - export PROJECT_NAME=Api
        - echo $NUGET_CONFIG_XML > ./nuget.config 
        - dotnet restore ./src/${PROJECT_NAME}/${PROJECT_NAME}.csproj
        - dotnet clean ./src/${PROJECT_NAME} --configuration $CONFIGURATION
        - dotnet build --no-restore ./src/${PROJECT_NAME} --configuration $CONFIGURATION /p:Version=${BITBUCKET_TAG}
        - dotnet publish --configuration $CONFIGURATION --no-build ./src/${PROJECT_NAME} -o ./publish_web_service
        - ls ./publish_web_service
      artifacts: 
        - publish_web_service/**

image: mcr.microsoft.com/dotnet/sdk:8.0
pipelines:
  default:
    - step: *build-web-service
  tags:
    '*':
      - step: *build-web-service-tag
      - step:
          name: Upload to FTP(WebService)
          runs-on: *self-hosted
          script: 
            - export PROJECT_NAME=ScenarioSwitcher
            - mkdir -p ./ftp/${PROJECT_NAME}/${BITBUCKET_TAG}
            - mv ./publish_web_service/* ./ftp/${PROJECT_NAME}/${BITBUCKET_TAG}
            - ls ./ftp/${PROJECT_NAME}/${BITBUCKET_TAG}/
            - pipe: atlassian/ftp-deploy:0.3.7
              variables:
                USER: $GFRELEASE_FTP_USER
                PASSWORD: $GFRELEASE_FTP_PASSWORD
                SERVER: $GFRELEASE_FTP_SERVER
                REMOTE_PATH: /gfrelease/Global/Customers/SapGarden/${PROJECT_NAME}/
                LOCAL_PATH: ./ftp/ # Optional
                # DEBUG: '<boolean>' # Optional
                # EXTRA_ARGS: '' # Optional.
                DELETE_FLAG: 'false' # Optional.