﻿using Grassfish.Consulting.Bypass.Clients.Api.V116;
using Grassfish.Consulting.Ixm;
using Grassfish.Consulting.Ixm.Clients;
using Grassfish.SapGarden.ScenarioSwitcher.Api.Services;

using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace Grassfish.SapGarden.ScenarioSwitcher.Api.Controllers.Users;

[ApiController]
[Route("[controller]")]
public class UsersController : ControllerBase
{
    private readonly ILogger<UsersController> _logger;
    private readonly IIxmService _ixm;
    private readonly IIxmApiClient ixmApi;

    public UsersController(ILogger<UsersController> logger, IIxmApiClient ixmApi)
    {
        this._logger = logger;
        this.ixmApi = ixmApi;
    }

    [AllowAnonymous]
    [HttpPost("[action]")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<Guid>> LoginAsync([FromBody] Consulting.Ixm.Core.IxmUsernamePasswordLoginRequest request, CancellationToken cancellationToken = default)
    {
        if (!request.IsValid)
        {
            return this.BadRequest("Login request is not valid!");
        }

        Guid token = await this.ixmApi.LoginAsync(
            request,
            cancellationToken: cancellationToken
        );

        if (token == default)
        {
            return this.Unauthorized();
        }

        return token;
    }

    [HttpPost("[action]")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status401Unauthorized)]
    public async Task<ActionResult<string>> GetJwtTokenAsync(CancellationToken cancellationToken = default)
    {
        string? token = await this.ixmApi.GetJwtTokenAsync(cancellationToken: cancellationToken);

        if (string.IsNullOrWhiteSpace(token))
        {
            return this.Unauthorized();
        }

        return token;
    }
}
