﻿using Microsoft.Extensions.DependencyInjection;

namespace Grassfish.SapGarden.ScenarioSwitcher.Application;

public static class DependencyInjection
{
    public static IServiceCollection AddApplication(this IServiceCollection services)
    {
        var myAssembly = typeof(DependencyInjection).Assembly;

        // Register MediatR DI
        services.AddMediatR(config => config.RegisterServicesFromAssembly(myAssembly));

        return services;
    }
}
