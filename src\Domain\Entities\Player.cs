﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Grassfish.SapGarden.ScenarioSwitcher.Domain.Entities;
public class Player
{
    [Key]
    public int Id { get; set; }

    public int IxmId { get; set; }

    public string IxmName { get; set; } = string.Empty;


    public virtual ICollection<ScenarioScreenLayoutPlayer> ScenarioScreenLayoutPlayers { get; set; }
}
