# SAP Garden Scenario Switcher - Test Case Matrix

## Test Environment Setup

- **Base URL**: `https://your-api-domain.com`
- **Authentication**: Required for most endpoints (except login)
- **Content-Type**: `application/json`

---

## 1. Authentication & Authorization Tests

| Test ID  | Test Case                           | HTTP Method | Endpoint             | Request Body                                                                            | Expected Status | Expected Response               | Notes                         |
| -------- | ----------------------------------- | ----------- | -------------------- | --------------------------------------------------------------------------------------- | --------------- | ------------------------------- | ----------------------------- |
| AUTH-001 | Valid Login                         | POST        | `/Users/<USER>"Username": "validuser", "Password": "validpass", "Application": "ScenarioSwitcher"}` | 200             | `"guid-token"`                  | Returns authentication token  |
| AUTH-002 | Invalid Login - Wrong Password      | POST        | `/Users/<USER>"Username": "validuser", "Password": "wrongpass", "Application": "ScenarioSwitcher"}` | 401             | `Unauthorized`                  | Authentication should fail    |
| AUTH-003 | Invalid Login - Missing Username    | POST        | `/Users/<USER>"Password": "validpass", "Application": "ScenarioSwitcher"}`                          | 400             | `"Login request is not valid!"` | Validation error              |
| AUTH-004 | Invalid Login - Empty Request       | POST        | `/Users/<USER>"Login request is not valid!"` | Validation error              |
| AUTH-005 | Get JWT Token - Authenticated       | POST        | `/Users/<USER>"jwt-token-string"`            | Requires valid session        |
| AUTH-006 | Get JWT Token - Unauthenticated     | POST        | `/Users/<USER>
| AUTH-007 | Access Protected Endpoint - No Auth | GET         | `/Scenarios`         | N/A                                                                                     | 401             | `Unauthorized`                  | Should require authentication |

---

## 2. Scenarios Management Tests

| Test ID  | Test Case                     | HTTP Method | Endpoint                 | Request Body                       | Expected Status | Expected Response                         | Notes                                    |
| -------- | ----------------------------- | ----------- | ------------------------ | ---------------------------------- | --------------- | ----------------------------------------- | ---------------------------------------- |
| SCEN-001 | Get All Scenarios - Empty     | GET         | `/Scenarios`             | N/A                                | 204             | No Content                                | When no scenarios exist                  |
| SCEN-002 | Get All Scenarios - With Data | GET         | `/Scenarios`             | N/A                                | 200             | `[{"Id": "guid", "Name": "string"}]`      | Returns scenario list                    |
| SCEN-003 | Create Scenario - Valid Name  | POST        | `/Scenarios`             | `"Test Scenario"`                  | 200             | `{"Id": "guid", "Name": "Test Scenario"}` | **⚠️ Bug: GUID generation issue**        |
| SCEN-004 | Create Scenario - Empty Name  | POST        | `/Scenarios`             | `""`                               | 200/400         | Varies                                    | **⚠️ Missing validation**                |
| SCEN-005 | Create Scenario - Null Name   | POST        | `/Scenarios`             | `null`                             | 400             | Error                                     | Should validate input                    |
| SCEN-006 | Create Scenario - Long Name   | POST        | `/Scenarios`             | `"Very long name..."` (>255 chars) | 200/400         | Varies                                    | **⚠️ Missing validation**                |
| SCEN-007 | Update Scenario - Valid       | PUT         | `/Scenarios/{validId}`   | `"Updated Name"`                   | 200             | `{"Id": "guid", "Name": "Updated Name"}`  | **⚠️ Bug: Returns ID instead of object** |
| SCEN-008 | Update Scenario - Invalid ID  | PUT         | `/Scenarios/{invalidId}` | `"Updated Name"`                   | 404             | `"Scenario not found"`                    | Should handle not found                  |
| SCEN-009 | Update Scenario - Empty Name  | PUT         | `/Scenarios/{validId}`   | `""`                               | 200/400         | Varies                                    | **⚠️ Missing validation**                |
| SCEN-010 | Delete Scenario - Valid ID    | DELETE      | `/Scenarios/{validId}`   | N/A                                | 200             | `{"Id": "guid", "Name": "string"}`        | Returns deleted scenario                 |
| SCEN-011 | Delete Scenario - Invalid ID  | DELETE      | `/Scenarios/{invalidId}` | N/A                                | 404             | `"Scenario not found"`                    | Should handle not found                  |
| SCEN-012 | Delete Scenario - In Use      | DELETE      | `/Scenarios/{inUseId}`   | N/A                                | 200/409         | Varies                                    | **⚠️ May need business rule**            |

---

## 3. Scenario Activation Tests

| Test ID | Test Case                                  | HTTP Method | Endpoint                          | Request Body | Expected Status | Expected Response | Notes                                 |
| ------- | ------------------------------------------ | ----------- | --------------------------------- | ------------ | --------------- | ----------------- | ------------------------------------- |
| ACT-001 | Activate Scenario - Valid with Assignments | POST        | `/Scenarios/{validId}/activate`   | N/A          | 200             | `OK`              | Should push to IXM system             |
| ACT-002 | Activate Scenario - No Assignments         | POST        | `/Scenarios/{validId}/activate`   | N/A          | 200             | `OK`              | Should handle empty scenario          |
| ACT-003 | Activate Scenario - Invalid ID             | POST        | `/Scenarios/{invalidId}/activate` | N/A          | 400/404         | Error             | Should validate scenario exists       |
| ACT-004 | Activate Scenario - IXM System Down        | POST        | `/Scenarios/{validId}/activate`   | N/A          | 500/400         | Error             | Should handle external system failure |

---

## 4. Screen Layout Assignment Tests

| Test ID | Test Case                                      | HTTP Method | Endpoint                                                   | Request Body | Expected Status | Expected Response      | Notes                         |
| ------- | ---------------------------------------------- | ----------- | ---------------------------------------------------------- | ------------ | --------------- | ---------------------- | ----------------------------- |
| SLA-001 | Get Assigned Screen Layouts - Empty            | GET         | `/Scenarios/{validId}/screen-layouts`                      | N/A          | 204             | No Content             | No assignments                |
| SLA-002 | Get Assigned Screen Layouts - With Data        | GET         | `/Scenarios/{validId}/screen-layouts`                      | N/A          | 200             | `[1, 2, 3]`            | Array of screen layout IDs    |
| SLA-003 | Get Assigned Screen Layouts - Invalid Scenario | GET         | `/Scenarios/{invalidId}/screen-layouts`                    | N/A          | 404             | `"Scenario not found"` | Should validate scenario      |
| SLA-004 | Set Screen Layout Players - Valid              | PUT         | `/Scenarios/{validId}/screen-layouts/{layoutId}/players`   | `[1, 2, 3]`  | 200             | `OK`                   | Assign players to layout      |
| SLA-005 | Set Screen Layout Players - Empty Array        | PUT         | `/Scenarios/{validId}/screen-layouts/{layoutId}/players`   | `[]`         | 200             | `OK`                   | Should clear assignments      |
| SLA-006 | Set Screen Layout Players - Invalid Scenario   | PUT         | `/Scenarios/{invalidId}/screen-layouts/{layoutId}/players` | `[1, 2, 3]`  | 404             | `"Scenario not found"` | Should validate scenario      |
| SLA-007 | Set Screen Layout Players - Invalid Players    | PUT         | `/Scenarios/{validId}/screen-layouts/{layoutId}/players`   | `[999, 888]` | 200/400         | Varies                 | **⚠️ May need validation**    |
| SLA-008 | Remove Screen Layout - Valid                   | DELETE      | `/Scenarios/{validId}/screen-layouts/{layoutId}`           | N/A          | 200             | `OK`                   | **⚠️ Bug: Wrong method name** |
| SLA-009 | Remove Screen Layout - Invalid Scenario        | DELETE      | `/Scenarios/{invalidId}/screen-layouts/{layoutId}`         | N/A          | 404             | `"Scenario not found"` | Should validate scenario      |
| SLA-010 | Get Screen Layout Players - Valid              | GET         | `/Scenarios/{validId}/screen-layouts/{layoutId}/players`   | N/A          | 200             | `[1, 2, 3]`            | Returns player IDs            |
| SLA-011 | Get Screen Layout Players - Empty              | GET         | `/Scenarios/{validId}/screen-layouts/{layoutId}/players`   | N/A          | 204             | No Content             | No players assigned           |

---

## 4.1. Missing Screen Layout Relationship Tests

| Test ID | Test Case                                   | HTTP Method | Endpoint                                      | Request Body | Expected Status | Expected Response                                            | Notes                                    |
| ------- | ------------------------------------------- | ----------- | --------------------------------------------- | ------------ | --------------- | ------------------------------------------------------------ | ---------------------------------------- |
| REL-001 | Find Scenarios Using Screen Layout          | GET         | `/ScreenLayouts/{layoutId}/scenarios`         | N/A          | 200/404         | `[{"Id": "guid", "Name": "scenario1"}]`                      | **⚠️ Missing endpoint**                  |
| REL-002 | Get All Scenario Assignments Overview       | GET         | `/Scenarios/assignments`                      | N/A          | 200             | `[{"ScenarioId": "guid", "ScreenLayouts": [1,2,3]}]`         | **⚠️ Missing endpoint**                  |
| REL-003 | Get Screen Layout Usage Statistics          | GET         | `/ScreenLayouts/usage`                        | N/A          | 200             | `[{"LayoutId": 1, "UsedInScenarios": 3, "TotalPlayers": 5}]` | **⚠️ Missing endpoint**                  |
| REL-004 | Find Scenarios with Conflicting Assignments | GET         | `/Scenarios/conflicts`                        | N/A          | 200             | `[{"Player": 1, "Scenarios": ["guid1", "guid2"]}]`           | **⚠️ Missing endpoint - Business Logic** |
| REL-005 | Get Complete Scenario-Layout-Player Matrix  | GET         | `/Scenarios/{scenarioId}/complete-assignment` | N/A          | 200             | `{"Scenario": {...}, "Assignments": [...]}`                  | **⚠️ Enhanced endpoint needed**          |

---

## 5. Players Management Tests

| Test ID | Test Case                         | HTTP Method | Endpoint   | Request Body | Expected Status | Expected Response                      | Notes                          |
| ------- | --------------------------------- | ----------- | ---------- | ------------ | --------------- | -------------------------------------- | ------------------------------ |
| PLY-001 | Get All Players - Success         | GET         | `/Players` | N/A          | 200             | `[{"IxmId": 1, "IxmName": "Player1"}]` | From IXM system                |
| PLY-002 | Get All Players - Empty           | GET         | `/Players` | N/A          | 204             | No Content                             | No players available           |
| PLY-003 | Get All Players - IXM System Down | GET         | `/Players` | N/A          | 400/500         | Error                                  | Should handle external failure |
| PLY-004 | Get All Players - Unauthenticated | GET         | `/Players` | N/A          | 401             | `Unauthorized`                         | Requires authentication        |

---

## 6. Screen Layouts Management Tests

| Test ID | Test Case                                | HTTP Method | Endpoint         | Request Body | Expected Status | Expected Response                      | Notes                          |
| ------- | ---------------------------------------- | ----------- | ---------------- | ------------ | --------------- | -------------------------------------- | ------------------------------ |
| SCL-001 | Get All Screen Layouts - Success         | GET         | `/ScreenLayouts` | N/A          | 200             | `[{"IxmId": 1, "IxmName": "Layout1"}]` | From IXM system                |
| SCL-002 | Get All Screen Layouts - Empty           | GET         | `/ScreenLayouts` | N/A          | 204             | No Content                             | No layouts available           |
| SCL-003 | Get All Screen Layouts - IXM System Down | GET         | `/ScreenLayouts` | N/A          | 400/500         | Error                                  | Should handle external failure |
| SCL-004 | Get All Screen Layouts - Unauthenticated | GET         | `/ScreenLayouts` | N/A          | 401             | `Unauthorized`                         | Requires authentication        |

---

## 7. Edge Cases & Error Handling Tests

| Test ID | Test Case                   | HTTP Method | Endpoint                  | Request Body      | Expected Status | Expected Response       | Notes                                |
| ------- | --------------------------- | ----------- | ------------------------- | ----------------- | --------------- | ----------------------- | ------------------------------------ |
| EDG-001 | Invalid GUID Format         | GET         | `/Scenarios/invalid-guid` | N/A               | 400             | `Bad Request`           | Should validate GUID format          |
| EDG-002 | Malformed JSON              | POST        | `/Scenarios`              | `{invalid json}`  | 400             | `Bad Request`           | Should handle JSON parsing errors    |
| EDG-003 | Large Request Body          | POST        | `/Scenarios`              | Very large string | 413/400         | Error                   | Should handle size limits            |
| EDG-004 | Concurrent Scenario Updates | PUT         | `/Scenarios/{id}`         | Different names   | 200             | Last wins               | **⚠️ May need concurrency handling** |
| EDG-005 | Database Connection Lost    | GET         | `/Scenarios`              | N/A               | 500             | `Internal Server Error` | Should handle DB failures gracefully |

---

## 8. Integration & Business Logic Tests

| Test ID | Test Case                     | Description                                 | Steps                                                                                                                             | Expected Result                        | Priority |
| ------- | ----------------------------- | ------------------------------------------- | --------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------- | -------- |
| INT-001 | Complete Scenario Workflow    | End-to-end scenario creation and activation | 1. Login<br>2. Create scenario<br>3. Get players<br>4. Get screen layouts<br>5. Assign players to layouts<br>6. Activate scenario | All steps succeed, IXM system updated  | High     |
| INT-002 | Multiple Scenarios Management | Create and manage multiple scenarios        | 1. Create 3 scenarios<br>2. Assign different layouts to each<br>3. Activate one<br>4. Verify others unaffected                    | Independent scenario management        | High     |
| INT-003 | Player Assignment Conflicts   | Test overlapping player assignments         | 1. Create 2 scenarios<br>2. Assign same player to both<br>3. Activate first scenario<br>4. Activate second scenario               | Last activation wins (verify behavior) | Medium   |
| INT-004 | IXM System Recovery           | Test behavior when IXM system recovers      | 1. Simulate IXM downtime<br>2. Attempt operations<br>3. Restore IXM<br>4. Retry operations                                        | Graceful recovery and retry            | Medium   |
| INT-005 | Large Scale Operations        | Test with many players/layouts              | 1. Create scenario<br>2. Assign 50+ players<br>3. Assign 10+ layouts<br>4. Activate scenario                                      | Performance and stability              | Low      |

---

## 9. Known Issues & Bug Verification Tests

| Test ID | Bug Description            | Test Steps                             | Current Behavior                                     | Expected Fix                          |
| ------- | -------------------------- | -------------------------------------- | ---------------------------------------------------- | ------------------------------------- |
| BUG-001 | GUID Generation Issue      | Create scenario and check ID           | Returns `00000000-0000-0000-0000-000000000000`       | Should return unique GUID             |
| BUG-002 | Update Returns Wrong Value | Update scenario name                   | Returns scenario ID instead of object                | Should return updated scenario object |
| BUG-003 | Wrong Method Name          | DELETE screen layout endpoint          | Method named `SetScenarioScreenLayoutPlayers`        | Should be renamed to match action     |
| BUG-004 | Async/Sync Inconsistency   | Delete scenario                        | Uses `SaveChanges()` instead of `SaveChangesAsync()` | Should use async version              |
| BUG-005 | Middleware Order           | Access protected endpoint without auth | May not properly authenticate                        | Should return 401 consistently        |

---

## 10. Performance & Load Tests

| Test ID  | Test Case                  | Description                              | Load Parameters              | Success Criteria             |
| -------- | -------------------------- | ---------------------------------------- | ---------------------------- | ---------------------------- |
| PERF-001 | Concurrent Logins          | Multiple users login simultaneously      | 10 concurrent requests       | All succeed within 5 seconds |
| PERF-002 | Scenario Creation Load     | Create many scenarios rapidly            | 50 scenarios in 30 seconds   | All created successfully     |
| PERF-003 | Activation Performance     | Activate scenarios with many assignments | 100+ player assignments      | Completes within 30 seconds  |
| PERF-004 | Database Query Performance | Get scenarios with large dataset         | 1000+ scenarios in DB        | Response within 2 seconds    |
| PERF-005 | Memory Usage               | Long-running operations                  | 24-hour continuous operation | No memory leaks              |

---

## Test Execution Guidelines

### Prerequisites

1. **Environment Setup**: Ensure test environment is properly configured
2. **Test Data**: Prepare test scenarios, players, and screen layouts
3. **IXM System**: Verify IXM system connectivity and test accounts
4. **Database**: Clean database state for each test run

### Test Execution Order

1. **Authentication Tests** (AUTH-001 to AUTH-007)
2. **Basic CRUD Tests** (SCEN-001 to SCEN-012)
3. **Integration Tests** (Players, Screen Layouts)
4. **Business Logic Tests** (Assignments, Activation)
5. **Edge Cases and Error Handling**
6. **Performance Tests** (if applicable)

### Bug Priority Classification

- **🔴 Critical**: Prevents core functionality (BUG-001, BUG-002)
- **🟡 High**: Affects user experience (BUG-003, BUG-004)
- **🟢 Medium**: Minor issues or improvements (BUG-005)

### Test Result Documentation

For each test case, document:

- ✅ **Pass**: Expected behavior observed
- ❌ **Fail**: Issue found, include details
- ⚠️ **Warning**: Unexpected behavior, needs investigation
- 🚫 **Blocked**: Cannot execute due to dependencies

---

## Contact Information

- **Development Team**: [Your team contact]
- **Test Environment**: [Environment details]
- **Issue Reporting**: [Bug tracking system]
