﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.4.33403.182
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{D5F8D73B-C775-4F44-95D8-978759DB3C1A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Api", "src\Api\Api.csproj", "{AF71FBFC-529C-4E52-9CD4-5F86DAD13333}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Domain", "src\Domain\Domain.csproj", "{84B297FA-3278-4BFC-A2C1-4515A2BA900D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Application", "src\Application\Application.csproj", "{982B57DA-1CF9-454E-905C-70807E7059D6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Grassfish.Consulting.Auth", "Grassfish.Consulting.Auth\Grassfish.Consulting.Auth.csproj", "{6DB263A4-615D-497A-A2E3-B1E105606E58}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Grassfish.Consulting.Ixm.Clients", "Grassfish.Consulting.Bypass\Grassfish.Consulting.Ixm.Clients.csproj", "{2D3B53E1-8CA3-4D67-A449-B8F83ADAE3BA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Grassfish.Consulting.Ixm.Core", "Grassfish.Consulting.Ixm.Core\Grassfish.Consulting.Ixm.Core.csproj", "{3512ECC5-328C-4D5E-9CD4-D05499117F29}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{AF71FBFC-529C-4E52-9CD4-5F86DAD13333}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF71FBFC-529C-4E52-9CD4-5F86DAD13333}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF71FBFC-529C-4E52-9CD4-5F86DAD13333}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF71FBFC-529C-4E52-9CD4-5F86DAD13333}.Release|Any CPU.Build.0 = Release|Any CPU
		{84B297FA-3278-4BFC-A2C1-4515A2BA900D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{84B297FA-3278-4BFC-A2C1-4515A2BA900D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{84B297FA-3278-4BFC-A2C1-4515A2BA900D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{84B297FA-3278-4BFC-A2C1-4515A2BA900D}.Release|Any CPU.Build.0 = Release|Any CPU
		{982B57DA-1CF9-454E-905C-70807E7059D6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{982B57DA-1CF9-454E-905C-70807E7059D6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{982B57DA-1CF9-454E-905C-70807E7059D6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{982B57DA-1CF9-454E-905C-70807E7059D6}.Release|Any CPU.Build.0 = Release|Any CPU
		{6DB263A4-615D-497A-A2E3-B1E105606E58}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6DB263A4-615D-497A-A2E3-B1E105606E58}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6DB263A4-615D-497A-A2E3-B1E105606E58}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6DB263A4-615D-497A-A2E3-B1E105606E58}.Release|Any CPU.Build.0 = Release|Any CPU
		{2D3B53E1-8CA3-4D67-A449-B8F83ADAE3BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2D3B53E1-8CA3-4D67-A449-B8F83ADAE3BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2D3B53E1-8CA3-4D67-A449-B8F83ADAE3BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2D3B53E1-8CA3-4D67-A449-B8F83ADAE3BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{3512ECC5-328C-4D5E-9CD4-D05499117F29}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3512ECC5-328C-4D5E-9CD4-D05499117F29}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3512ECC5-328C-4D5E-9CD4-D05499117F29}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3512ECC5-328C-4D5E-9CD4-D05499117F29}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{AF71FBFC-529C-4E52-9CD4-5F86DAD13333} = {D5F8D73B-C775-4F44-95D8-978759DB3C1A}
		{84B297FA-3278-4BFC-A2C1-4515A2BA900D} = {D5F8D73B-C775-4F44-95D8-978759DB3C1A}
		{982B57DA-1CF9-454E-905C-70807E7059D6} = {D5F8D73B-C775-4F44-95D8-978759DB3C1A}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {8A5AC95F-769E-43CB-A923-DDDBB6130A29}
	EndGlobalSection
EndGlobal
