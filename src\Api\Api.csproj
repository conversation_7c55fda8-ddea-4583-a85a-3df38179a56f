﻿<Project Sdk="Microsoft.NET.Sdk.Web">
	<PropertyGroup>
		<UserSecretsId>e8d26ba3-864e-43e3-bed3-143b8a804fef</UserSecretsId>
		<DockerDefaultTargetOS>Windows</DockerDefaultTargetOS>
		<DockerfileContext>..\..</DockerfileContext>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Grassfish.Extensions.Configuration.MasterConfig" Version="8.0.0" />
		<PackageReference Include="Grassfish.Extensions.Logging.Serilog.Server" Version="8.0.0" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.7" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.7" />
		<PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.0" />
		<PackageReference Include="Serilog" Version="4.0.1" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="6.0.0" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\..\Grassfish.Consulting.Auth\Grassfish.Consulting.Auth.csproj" />
		<ProjectReference Include="..\..\Grassfish.Consulting.Bypass\Grassfish.Consulting.Ixm.Clients.csproj" />
		<ProjectReference Include="..\Application\Application.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Content Update="App.config">
			<CopyToOutputDirectory>Never</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Data\AppDb\Migrations\" />
	</ItemGroup>
</Project>
