﻿using Microsoft.AspNetCore.Authentication;

namespace Grassfish.Consulting.Auth;

public class IxmAuthenticationSchemeOptions : AuthenticationSchemeOptions
{
    public const string SCHEME_NAME = "IxmScheme";
    public const string HEADER_XAPIKEY = "X-ApiKey";
    public const string HEADER_XSESSIONID = "X-Session-Id";
    public const string HEADER_AUTHORIZATION = "Authorization";
    public const string CLAIM_ID = "Id";
    public const string CLAIM_USER_TYPE = "User Type";
    public const string CLAIM_CUSTOMER_ID = "Customer Id";
}
